name: Write release notes

on:
  pull_request:
    types: [opened, reopened, synchronize, edited, closed, labeled]

permissions:
  contents: write
  pull-requests: write

jobs:
  Release:
    if: contains(github.event.pull_request.labels.*.name, 'release')
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository code
        uses: actions/checkout@v3

      - name: Set RELEASE env var from package.json
        run: echo RELEASE=$(node --print "require('./package.json').version") >> $GITHUB_ENV

      - name: Create release notes 
        uses: release-drafter/release-drafter@v5
        with:
          commitish: main
          prerelease: false
          tag: ${{ env.RELEASE }}
          name: ${{ env.RELEASE }}
          version: ${{ env.RELEASE }}
          latest: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
