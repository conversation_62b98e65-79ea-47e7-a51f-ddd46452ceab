name: Release Pieces

on:
  workflow_dispatch:
  push:
    branches:
      - main
    paths:
      - 'packages/pieces/**'
      - 'packages/shared/**'

jobs:
  Release-Pieces:
    if: github.repository == 'activepieces/activepieces'
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: npm-${{ hashFiles('package-lock.json') }}
          restore-keys: npm-

      - name: Install dependencies
        run: npm ci --ignore-scripts

      - name: build packages
        run: npx nx run-many --target=build

      - name: copy project .npmrc to user level
        run: cp .npmrc $HOME/.npmrc

      - name: publish shared package
        run: npx ts-node -r tsconfig-paths/register -P packages/engine/tsconfig.lib.json tools/scripts/utils/publish-nx-project.ts packages/shared
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: publish pieces-common package
        run: npx ts-node -r tsconfig-paths/register -P packages/engine/tsconfig.lib.json tools/scripts/utils/publish-nx-project.ts packages/pieces/community/common
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: publish pieces-framework package
        run: npx ts-node -r tsconfig-paths/register -P packages/engine/tsconfig.lib.json tools/scripts/utils/publish-nx-project.ts packages/pieces/community/framework
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: publish pieces packages
        run: npx ts-node -r tsconfig-paths/register -P packages/engine/tsconfig.lib.json tools/scripts/pieces/publish-pieces-to-npm.ts
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: update pieces metadata
        run: npx ts-node -r tsconfig-paths/register -P packages/engine/tsconfig.lib.json tools/scripts/pieces/update-pieces-metadata.ts packages/pieces/community/framework
        env:
          AP_CLOUD_API_KEY: ${{ secrets.AP_CLOUD_API_KEY }}
