name: Release RC

on:
  workflow_dispatch:

jobs:
  Release:
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository code
        uses: actions/checkout@v3

      - name: Set RELEASE env var from package.json
        run: echo RELEASE=$(node --print "require('./package.json').rcVersion") >> $GITHUB_ENV

      - name: Set CLOUD_RELEASE env var from package.json
        run: echo CLOUD_RELEASE=$(node --print "require('./package.json').rcVersion.replace(/-/g, '')") >> $GITHUB_ENV

      - name: Fail if tag already exists
        run: '! docker manifest inspect activepieces/activepieces:${{ env.RELEASE }}'

      - name: Set up Depot CLI
        uses: depot/setup-action@v1

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push
        uses: depot/build-push-action@v1
        with:
          project: du7O4b0e8P
          token: ${{ secrets.DEPOT_PROJECT_TOKEN }}
          context: .
          file: ./Dockerfile
          platforms: |
            linux/amd64
            linux/arm64
          push: true
          tags: |
            ghcr.io/activepieces/activepieces:${{ env.RELEASE }}
            ghcr.io/activepieces/activepieces-cloud:${{ env.CLOUD_RELEASE }}
