name: Closed Issue Message
on:
  issues:
    types: [closed]
permissions: {}
jobs:
  auto_comment:
    permissions:
      issues: write # to comment on issues (aws-actions/closed-issue-message)

    runs-on: ubuntu-latest
    if: github.repository_owner == 'activepieces'
    steps:
      - uses: aws-actions/closed-issue-message@v1
        with:
          # These inputs are both required
          repo-token: "${{ secrets.GITHUB_TOKEN }}"
          message: |
            ### ⚠️COMMENT VISIBILITY WARNING⚠️
            Comments on closed issues are hard for our team to see.
            If this issue is continuing with the latest stable version of Activepieces, please open a new issue that references this one.