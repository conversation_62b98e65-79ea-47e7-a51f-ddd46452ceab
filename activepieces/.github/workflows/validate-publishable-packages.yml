name: Validate publishable packages

on:
  workflow_dispatch:
  pull_request:
    branches:
      - main
    paths:
      - 'packages/pieces/**'

jobs:
  validate-publishable-packages:
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Cache dependencies
        id: cache-npm
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: npm-${{ hashFiles('package-lock.json') }}
          restore-keys: npm-

      - if: ${{ steps.cache-npm.outputs.cache-hit != 'true' }}
        name: List the state of node modules
        continue-on-error: true
        run: npm list

      - name: Install dependencies
        run: npm ci --ignore-scripts

      - name: validate publishable packages
        run: npx ts-node -r tsconfig-paths/register -P packages/engine/tsconfig.lib.json tools/scripts/validate-publishable-packages.ts
