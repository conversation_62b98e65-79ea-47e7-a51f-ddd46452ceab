name: "Issue Linking - Require Issue Reference"

on:
  pull_request_target:
    types:
      - opened
      - edited
      - synchronize

permissions:
  pull-requests: read
  issues: read

jobs:
  validate-issue-linking:
    name: Check Issue Linking
    runs-on: ubuntu-latest
    steps:
      - name: Check Issue Linking
        uses: actions/github-script@v7
        with:
          script: |
            const body = context.payload.pull_request.body || '';
            const issuePattern = /(?:closes|fixes|resolves)\s+#(\d+)/i;
            const linkedIssue = body.match(issuePattern);
            
            if (!linkedIssue) {
              core.setFailed('Pull request must be linked to an issue using "closes #issue_number", "fixes #issue_number", or "resolves #issue_number"');
            } 