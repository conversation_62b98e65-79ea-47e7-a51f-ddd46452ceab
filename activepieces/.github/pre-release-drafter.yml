include-pre-releases: true
exclude-labels:
  - 'skip-changelog'
  - 'release'
  - 'pre-release'
categories:
  - title: "⛓️‍💥 Breaking Changes"
    labels:
      - "⛓️‍💥 breaking-change"
  - title: "✨ Exciting New Features"
    labels:
      - "🌟 feature"
  - title: "🧩 Pieces"
    labels:
      - "🔌 pieces"
  - title: "🛠️ Piece Framework"
    labels:
      - "🛠️ piece-framework"
  - title: "🐞 Bug Fixes"
    labels:
      - "🐛 bug"
  - title: "🎨 Enhancements & Polish"
    labels:
      - "✨ polishing"
  - title: "📚 Documentation"
    labels:
      - "📚 documentation"
  - title: "🧹 Maintenance"
    labels:
      - "🧹 clean up"

template: |

  $CHANGES

  ## Thanks ❤️
  $CONTRIBUTORS