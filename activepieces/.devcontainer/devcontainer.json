// Update the VARIANT arg in docker-compose.yml to pick a Node.js version
{
	"name": "Activepieces Dev",
	"dockerComposeFile": "docker-compose.yml",
	"service": "app",
	"workspaceFolder": "/workspace",

	// Configure tool-specific properties.
	"customizations": {
		// Configure properties specific to VS Code.
		"vscode": {
			// Add the IDs of extensions you want installed when the container is created.
			"extensions": [
				"cipchk.cssrem",
				"huizhou.githd"
			]
		}
	},
	"forwardPorts": [3000, 4200, 5432],
	"postAttachCommand": "/bin/bash .devcontainer/setup.sh",
	"hostRequirements": {
  	  "cpus": 4,
  	  "memory": "8gb"
  	},

	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// This can be used to network with other containers or with the host.

	// Use 'postCreateCommand' to run commands after the container is created.
	// "postCreateCommand": "yarn install",
	"remoteUser": "root",
	"postCreateCommand": "npm ci"
}
