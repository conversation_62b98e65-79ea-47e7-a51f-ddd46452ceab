# Default values for activepieces.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# This will set the replicaset count more information can be found here: https://kubernetes.io/docs/concepts/workloads/controllers/replicaset/
replicaCount: 1

# This sets the container image more information can be found here: https://kubernetes.io/docs/concepts/containers/images/
image:
  repository: ghcr.io/activepieces/activepieces
  # This sets the pull policy for images.
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

# This is for the secrets for pulling an image from a private repository more information can be found here: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
imagePullSecrets: []
# This is to override the chart name.
nameOverride: ""
fullnameOverride: ""

# This section builds out the service account more information can be found here: https://kubernetes.io/docs/concepts/security/service-accounts/
serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

# This is for setting Kubernetes Annotations to a Pod.
# For more information checkout: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
podAnnotations: {}
# This is for setting Kubernetes Labels to a Pod.
# For more information checkout: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
podLabels: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

# Container configuration
container:
  port: 80

# This is for setting up a service more information can be found here: https://kubernetes.io/docs/concepts/services-networking/service/
service:
  # This sets the service type more information can be found here: https://kubernetes.io/docs/concepts/services-networking/service/#publishing-services-service-types
  type: ClusterIP
  # This sets the ports more information can be found here: https://kubernetes.io/docs/concepts/services-networking/service/#field-spec-ports
  port: 80

# This block is for setting up the ingress for more information can be found here: https://kubernetes.io/docs/concepts/services-networking/ingress/
ingress:
  enabled: false
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

# This is to setup the liveness and readiness probes more information can be found here: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/
livenessProbe:
  httpGet:
    path: /v1/health
    port: http
  initialDelaySeconds: 30
  periodSeconds: 10
readinessProbe:
  httpGet:
    path: /v1/health
    port: http
  initialDelaySeconds: 5
  periodSeconds: 5

# This section is for setting up autoscaling more information can be found here: https://kubernetes.io/docs/concepts/workloads/autoscaling/
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

persistence:
  enabled: true
  size: 2Gi
  mountPath: "/usr/src/app/cache"

volumes: []
volumeMounts: []

nodeSelector: {}
tolerations: []
affinity: {}

activepieces:
  frontendUrl: "http://localhost:4200"
  executionMode: "SANDBOX_CODE_ONLY"
  environment: "prod"
  telemetryEnabled: true
  templatesSourceUrl: "https://cloud.activepieces.com/api/v1/flow-templates"
  flowWorkerConcurrency: ""
  scheduledWorkerConcurrency: ""
  triggerDefaultPollInterval: ""
  triggerTimeoutSeconds: ""
  flowTimeoutSeconds: ""
  webhookTimeoutSeconds: ""
  executionDataRetentionDays: ""
  issueArchiveDays: ""
  pausedFlowTimeoutDays: ""
  logLevel: "info"
  logPretty: false
  apiRateLimiting:
    authn:
      enabled: false
      max: 100
      window: 60
  projectRateLimiter:
    enabled: false
  piecesSource: ""
  devPieces: false
  piecesSyncMode: ""

  # Misc
  clientRealIpHeader: ""
  apiKey: ""
  internalUrl: ""
  maxConcurrentJobsPerProject: ""
  perplexityBaseUrl: ""
  configPath: ""
  featurebaseApiKey: ""
  showChangelog: true
  enableFlowOnPublish: true
  appWebhookSecrets: ""
  maxFileSizeMb: ""
  sandboxMemoryLimit: ""
  sandboxPropagatedEnvVars: ""
  triggerFailuresThreshold: ""
  engineExecutablePath: "dist/packages/engine/main.js"

postgresql:
  enabled: true
  host: ""
  port: 5432
  useSSL: false
  url: ""
  auth:
    database: "activepieces"
    username: "postgres"
    # password is automatically generated if not provided
    password: ""
  primary:
    persistence:
      enabled: true

redis:
  enabled: true
  host: ""
  port: 6379
  useSSL: false
  db: 0
  url: ""
  type: "standalone"
  sentinel:
    enabled: false
    name: ""
    hosts: ""
    role: ""
  failedJob:
    retentionDays: 7
    retentionMaxCount: 100
  auth:
    enabled: true
    # password is automatically generated if not provided
    password: ""
  master:
    persistence:
      enabled: true

smtp:
  enabled: false
  host: ""
  port: 587
  username: ""
  password: ""
  senderEmail: ""
  senderName: ""

s3:
  enabled: false
  accessKeyId: ""
  secretAccessKey: ""
  bucket: ""
  endpoint: ""
  region: ""
  useSignedUrls: false
  useIrsa: false

queueUi:
  enabled: false
  username: ""
  password: ""
