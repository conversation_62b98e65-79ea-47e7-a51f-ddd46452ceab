---
title: "Technical limits"
icon: 'ban'
description: "technical limits for Activepieces execution"
---

### Overview

<Warning>
This Limits applies for the **Activepieces Cloud**, and can be configured via environment variables for self-hosted instances.
</Warning>

### Flow Limits

- **Execution Time**: Each flow has a maximum execution time of **600 seconds (10 minutes)**. Flows exceeding this limit will be marked as a timeout.
- **Memory Usage**: During execution, a flow should not use more than **128 MB of RAM**.

<Tip>
**Friendly Tip #1:** Flow run in a paused state, such as Wait for Approval or Delay, do not count toward the 600 seconds.
</Tip>

<Tip>
**Friendly Tip #2:** The execution time limit can be worked around by splitting the flows into multiple ones, such as by having one flow call another flow using a webhook, or by having each flow process a small batch of items.
</Tip>

### File Storage Limits
<Info>
The files from actions or triggers are stored in the database / S3 to support retries from certain steps.
</Info>

- **Maximum File Size**: 10 MB 

### Data Storage Limits

Some pieces utilize the built-in Activepieces key store, such as the Store Piece and Queue Piece.

The storage limits are as follows:

- **Maximum Key Length**: 128 characters
- **Maximum Value Size**: 512 KB


