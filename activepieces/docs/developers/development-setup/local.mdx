---
title: 'Local Dev Environment'
description: ''
---

## Prerequisites

- Node.js v18+
- npm v9+

## Instructions

1. Setup the environment

```bash
node tools/setup-dev.js
```

2. Start the environment

This command will start activepieces with sqlite3 and in memory queue.

```bash
npm start
```

<Note>
  By default, the development setup only builds specific pieces.Open the file
  `packages/server/api/.env` and add comma-separated list of pieces to make
  available.

For more details, check out the [Piece Development](/developers/development-setup/getting-started) section.

</Note>

3. Go to **_localhost:4200_** on your web browser and sign in with these details:

Email: `<EMAIL>`
Password: `12345678`
