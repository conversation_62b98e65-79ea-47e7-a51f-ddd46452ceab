---
title: 'Getting Started'
description: ''
---

## Development Setup 

To set up the development environment, you can choose one of the following methods:

- **Codespaces**: This is the quickest way to set up the development environment. Follow the [Codespaces](./codespaces) guide.
- **Local Environment**: It is recommended for local development. Follow the [Local Environment](./local) guide.
- **Dev Container**: This method is suitable for remote development on another machine. Follow the [Dev Container](./dev-container) guide.


## Pieces Development

To avoid making the dev environment slow, not all pieces are functional during development at first. By default, only these pieces are functional at first, as specified in `AP_DEV_PIECES`.

https://github.com/activepieces/activepieces/blob/main/packages/server/api/.env#L4

To override the default list available at first, define an `AP_DEV_PIECES` environment variable with a comma-separated list of pieces to make available. For example, to make `google-sheets` and `cal-com` available, you can use:

```sh
AP_DEV_PIECES=google-sheets,cal-com npm start
```
