---
title: 'GitHub Codespaces'
description: ''
---

GitHub Codespaces is a cloud development platform that enables developers to write, run, and debug code directly in their browsers, seamlessly integrated with GitHub.

### Steps to setup Codespaces

1. Go to [Activepieces repo](https://github.com/activepieces/activepieces).

2. Click Code `<>`, then under codespaces click create codespace on main.

![Create Codespace](/resources/screenshots/development-setup_codespaces.png)

<Note>
  By default, the development setup only builds specific pieces.Open the file
  `packages/server/api/.env` and add comma-separated list of pieces to make
  available.

For more details, check out the [Piece Development](/developers/development-setup/getting-started) section.

</Note>

3. Open the terminal and run `npm start`

4. Access the frontend URL by opening port 4200 and signing in with these details:

Email: `<EMAIL>`
Password: `12345678`
