---
title: 'Piece Versioning'
icon: 'code-compare'
description: 'Learn how to version your pieces'
---

Pieces are npm packages and follows **semantic versioning**.

## Semantic Versioning

The version number consists of three numbers: `MAJOR.MINOR.PATCH`, where:

- **MAJOR** It should be incremented when there are breaking changes to the piece.
- **MINOR** It should be incremented for new features or functionality that is compatible with the previous version, unless the major version is less than 1.0, in which case it can be a breaking change.
- **PATCH** It should be incremented for bug fixes and small changes that do not introduce new features or break backward compatibility.

## Engine
The engine will use the most up-to-date compatible version for a given piece version during the **DRAFT** flow versions. Once the flow is published, all pieces will be locked to a specific version.

**Case 1: Piece Version is Less Than 1.0**:
The engine will select the latest **patch** version that shares the same **minor** version number.

**Case 2: Piece Version Reaches Version 1.0**:
The engine will select the latest **minor** version that shares the same **major** version number.

## Examples
<Tip>
when you make a change, remember to increment the version accordingly.
</Tip>

### Breaking changes
- Remove an existing action.
- Add a required `action` prop.
- Remove an existing action prop, whether required or optional.
- Remove an attribute from an action output.
- Change the existing behavior of an action/trigger.

### Non-breaking changes
- Add a new action.
- Add an optional `action` prop.
- Add an attribute to an action output.

i.e., any removal is breaking, any required addition is breaking, everything else is not breaking.