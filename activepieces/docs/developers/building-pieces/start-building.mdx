---
title: 'Start Building'
icon: 'hammer'
description: ''
---
This section guides you in creating a Gelato piece, from setting up your development environment to contributing the piece. By the end of this tutorial, you will have a piece with an action that fetches a random ice cream flavor and a trigger that fetches newly created ice cream flavors.

<Info>
These are the next sections. In each step, we will do one small thing. This tutorial should take around 30 minutes.
</Info>

## Steps Overview 

<Steps>
  <Step title="Fork Repository" icon="code-branch">
    Fork the repository to create your own copy of the codebase.
  </Step>
  <Step title="Setup Development Environment" icon="code">
    Set up your development environment with the necessary tools and dependencies.
  </Step>
  <Step title="Create Piece Definition" icon="gear">
    Define the structure and behavior of your Gelato piece.
  </Step>
  <Step title="Add Piece Authentication" icon="lock">
    Implement authentication mechanisms for your Gelato piece.
  </Step>
  <Step title="Create Action" icon="ice-cream">
    Create an action that fetches a random ice cream flavor.
  </Step>
  <Step title="Create Trigger" icon="ice-cream">
    Create a trigger that fetches newly created ice cream flavors.
  </Step>
  <Step title="Sharing Pieces" icon="share">
    Share your Gelato piece with others.
  </Step>
</Steps>


<Card title="Contribution" icon="gift" iconType="duotone" color="#6e41e2">
 Contribute a piece to our repo and receive +1,400 tasks/month on [Activepieces Cloud](https://cloud.activepieces.com).
</Card>
