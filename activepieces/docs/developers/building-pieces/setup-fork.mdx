---
title: 'Fork Repository'
icon: "circle-1"
---

To start building pieces, we need to fork the repository that contains the framework library and the development environment. Later, we will publish these pieces as `npm` artifacts.

Follow these steps to fork the repository:

1. Go to the repository page at https://github.com/activepieces/activepieces.
2. Click the `Fork` button located in the top right corner of the page.

![Fork Repository](/resources/screenshots/fork-repository.jpg)


<Tip>
If you are an enterprise customer and want to use the private pieces feature, you can refer to the tutorial on how to set up a [private fork](../misc/private-fork).
</Tip>