---
title: "Contribute"
description: "Learn how to contribute a piece to the main repository."
---

<Steps>
   <Step title="Open a pull request">
     - Build and test your piece.
     - Open a pull request from your repository to the main fork.
     - A maintainer will review your work closely.
   </Step>
   <Step title="Merge the pull request">
   - Once the pull request is approved, it will be merged into the main branch.
   - Your piece will be available within a few minutes.
   - An automatic GitHub action will package it and create an npm package on npmjs.com.
   </Step>
</Steps>
