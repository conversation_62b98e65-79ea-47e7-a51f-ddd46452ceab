---
title: "Product Principles"
sidebarTitle: "Product Principles"
icon: "pen-nib"
---

## 🌟 Keep It Simple

- Design the product to be accessible for everyone, regardless of their background and technical expertise.

- The code is in a monorepository under one service, making it easy to develop, maintain, and scale.

- Keep the technology stack simple to achieve massive adoption.

- Keep the software unopinionated and unlock niche use cases by making it extensible through pieces.

## 🧩 Keep It Extensible

- Automation pieces framework has minimal abstraction and allow you to extend for any usecase.

- All contributions are welcome. The core is open source, and commercial code is available.
