---
title: "Our Team Structure" 
icon: 'users'
---

We are big believers in small teams with 10x engineers who would outperform other team types.

## No product management by default

Engineers decide what to build. If you need help, feel free to reach out to the team for other opinions or help. 

## No Process by default

We trust the engineers' judgment to make the call whether this code is risky and requires external approval or if it's a fix that can be easily reversed or fixed with no big impact on the end user.

## They Love Users

When the engineer loves the users, that means they would ship fast, they wouldn't over-engineer because they understand the requirements very well, they usually have empathy which means they don't complicate everyone else.

## Pragmatic & Speed

Engineering planning sometimes seems sexy from a technical perspective, but being pragmatic means you would take decisions in a timely manner, taking them in baby steps and iterating faster rather than planning for the long run, and it's easy to reverse wrong decisions early on without investing too much time. 

## Starts With Hiring

We hire very **slowly**. We are always looking for highly talented engineers. We love to hire people with a broader skill set and flexibility, low egos, and who are builders at heart.

We found that working with strong engineers is one of the strongest reasons to retain employees, and this would allow everyone to be free and have less process.
