---
title: "Engineering Workflow" 
icon: 'lightbulb'
---

Activepieces work is based on one-week sprints, as priorities change fast, the sprint has to be short to adapt. 

## Sprints

Sprints are shared publicly on our GitHub account. This would give everyone visibility into what we are working on.

* There should be a GitHub issue for the sprint set up in advance that outlines the changes.
* Each _individual_ should come prepared with specific suggestions for what they will work on over the next sprint. **if you're in an engineering role, no one will dictate to you what to build – it is up to you to drive this.**
* Teams generally meet once a week to pick the **priorities** together.
* Everyone in the team should attend the sprint planning.
* Anyone can comment on the sprint issue before or after the sprint.

## Pull Requests

When it comes to code review, we have a few guidelines to ensure efficiency:

- Create a pull request in draft state as soon as possible.
- Be proactive and review other people’s pull requests. Don’t wait for someone to ask for your review; it’s your responsibility.
- Assign only one reviewer to your pull request.
- Add the PR to the current project (sprint) so we can keep track of unmerged PRs at the end of each sprint.
- It is the **responsibility** of the **PR owner** to draft the test scenarios within the PR description. Upon review, the reviewer may assume that these scenarios have been tested and provide additional suggestions for scenarios.
- Large, incomplete features should be broken down into smaller tasks and continuously merged into the main branch.

## Planning is everyone's job.

Every engineer is responsible for discovering bugs/opportunities and bringing them up in the sprint to convert them into actionable tasks.
