---
title: "Run Enterprise Edition"
icon: "building"
---

The enterprise edition requires a postgres and redis instance to run, and a license key to activate.

<Steps>
<Step title="Run the dev container">

Follow the instructions [here](/developers/development-setup/dev-container) to run the dev container.
</Step>

<Step title="Add the following env variables in `server/api/.env`">

Pase the following env variables in `server/api/.env`

```bash
## these variables are set to align with the .devcontainer/docker-compose.yml file
AP_DB_TYPE=POSTGRES
AP_DEV_PIECES="your_piece_name"
AP_ENVIRONMENT="dev"
AP_EDITION=ee
AP_EXECUTION_MODE=SANDBOXED
AP_FRONTEND_URL="http://localhost:4200"
AP_WEBHOOK_URL="http://localhost:3000"
AP_PIECES_SOURCE='FILE'
AP_PIECES_SYNC_MODE='NONE'
AP_LOG_LEVEL=debug
AP_LOG_PRETTY=true
AP_QUEUE_MODE=REDIS
AP_REDIS_HOST="redis"
AP_REDIS_PORT="6379"
AP_TRIGGER_DEFAULT_POLL_INTERVAL=1
AP_CACHE_PATH=/workspace/cache
AP_POSTGRES_DATABASE=activepieces
AP_POSTGRES_HOST=db
AP_POSTGRES_PORT=5432
AP_POSTGRES_USERNAME=postgres
AP_POSTGRES_PASSWORD=A79Vm5D4p2VQHOp2gd5
AP_ENCRYPTION_KEY=427a130d9ffab21dc07bcd549fcf0966
AP_JWT_SECRET=secret
```

</Step>
<Step title="Activate Your License Key">
    After signing in, activate the license key by going to **Platform Admin -> Setup -> License Keys**
    ![Activation License Key](/resources/screenshots/activation-license-key-settings.png)
</Step>

</Steps>