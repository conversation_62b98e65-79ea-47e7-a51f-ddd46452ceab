name: Pre-Prod Automate Deploy

on:
  workflow_dispatch:
  push:
    branches:
      - main
  pull_request:
    types: [merged]
    branches:
      - main
    merged: true

jobs:
  Release:
    if: github.event_name == 'workflow_dispatch' || github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.pull_request.merged == true)
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository code
        uses: actions/checkout@v3

      - name: Set RELEASE env var from package.json
        run: echo RELEASE=$(node --print "require('./package.json').version") >> $GITHUB_ENV

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Depot CLI
        uses: depot/setup-action@v1

      - name: Build and push
        uses: depot/build-push-action@v1
        with:
          project: du7O4b0e8P
          token: ${{ secrets.DEPOT_PROJECT_TOKEN }}
          context: .
          file: ./Dockerfile
          platforms: |
            linux/amd64
          push: true
          tags: |
            ghcr.io/activepieces/activepieces:${{ env.RELEASE }}.${{ github.sha }}.pre-prod

      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh/
          echo "$SSH_KEY" > ~/.ssh/pre-prod.key
          chmod 600 ~/.ssh/pre-prod.key
          cat >>~/.ssh/config <<END
          Host pre-prod
            HostName $SSH_HOST
            User $SSH_USER
            IdentityFile ~/.ssh/pre-prod.key
            StrictHostKeyChecking no
          END
        env:
          SSH_USER: ${{ secrets.PRE_PROD_USERNAME }}
          SSH_KEY: ${{ secrets.PRE_PROD_SSH_PRIVATE_KEY }}
          SSH_HOST: ${{ secrets.PRE_PROD_HOST }}
          
      - name: Deploy with Docker Compose
        run: |
          ssh pre-prod -t -t 'bash -ic "cd activepieces && IMAGE_TAG=${{ env.RELEASE }}.${{ github.sha }}.pre-prod docker compose pull && IMAGE_TAG=${{ env.RELEASE }}.${{ github.sha }}.pre-prod docker compose up -d --force-recreate activepieces; exit"' 

  Pre-Prod-E2E-Tests:
    needs: Release
    uses: ./.github/workflows/e2e-tests.yml
    with:
      environment: Pre-Prod
    secrets:
      CHECKLY_API_KEY: ${{ secrets.CHECKLY_API_KEY }}
      CHECKLY_ACCOUNT_ID: ${{ secrets.CHECKLY_ACCOUNT_ID }}